import Image from "next/image"

export default function HeroSection() {
  return (
    <section className="w-full py-8 md:py-24">
      <div className="container mx-auto text-center px-4">
        <div className="flex justify-center mb-6 md:mb-8">
          <div className="logo-binary-container w-[100px] h-[100px] md:w-[148px] md:h-[148px]">
            {/* Base logo with reduced opacity */}
            <Image
              src="/white_horse_icon.svg"
              alt="DarkHorse Icon"
              width={72}
              height={78}
              className="logo-base object-contain w-full h-full"
            />

            {/* Binary code overlay that flows through the logo shape */}
            <div className="logo-binary-overlay">
              <div className="binary-stream">1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101</div>
              <div className="binary-stream">0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010</div>
              <div className="binary-stream">1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101</div>
              <div className="binary-stream">0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110</div>
              <div className="binary-stream">1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101</div>
              <div className="binary-stream">0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010\n0101101\n1010110\n0110101\n1101010</div>
              <div className="binary-stream">1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001\n1100110\n0011001</div>
              <div className="binary-stream">0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001\n0011110\n1100001</div>
              <div className="binary-stream">1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111</div>
              <div className="binary-stream">0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000\n0000111\n1111000</div>
            </div>
          </div>
        </div>

        <h1 className="text-[28px] md:text-6xl font-bold mb-3 md:mb-4 px-2">
          <span
            style={{
              background:
                "linear-gradient(0deg, #FFFFFF, #FFFFFF)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
            }}
          >
            DarkHorse Your Picks
          </span>
          <br />
          for an <span className="gradient-text">Unstoppable Edge</span>
        </h1>

        <p className="text-lg md:text-2xl mb-6 md:mb-10 max-w-3xl mx-auto">
          AI-Powered Sports Predictions for Smarter Decision-Making
        </p>

        <div className="flex flex-row justify-center flex-wrap gap-3 mb-8 md:mb-12">
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2">
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">100% Data-Driven Insights</span>
          </div>
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2">
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">Proven Accuracy</span>
          </div>
          <div className="bg-[#28282847] backdrop-blur-[20px] rounded-full px-6 py-2">
            <span className="text-[#99ACEA] font-['Inter'] text-[14px] tracking-[0%]">Real-Time Updates</span>
          </div>
        </div>

        <button className="bg-gradient-to-r from-[#8C01FA] to-[#19FB9B] text-white text-[14px] rounded-[88px] w-full max-w-xs md:w-[224px] h-[48px] px-[8px] py-[8px] flex items-center justify-center gap-[8px] hover:opacity-90 transition-opacity mb-8 md:mb-16 mx-auto">
          Get AI Predictions
        </button>

        <div className="max-w-5xl mx-auto mb-8 md:mb-16 flex flex-col md:flex-row items-center justify-center gap-8">
          <Image
            src="/Macbook.webp"
            alt="DarkHorse Desktop App"
            width={1600}
            height={900}
            className="w-full max-w-[1600px] h-auto rounded-lg shadow-lg transition-transform"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 max-w-6xl mx-auto">
          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto">
            <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center">
              NFL, NBA, MLB & MLS
              <br />
              Predictions
            </h3>
          </div>

          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto">
            <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center">
              Win Probabilities, Spreads,
              <br />& Totals Forecasts
            </h3>
          </div>

          <div className="bg-[#00000047] backdrop-blur-[20px] rounded-[24px] w-full md:w-[400px] h-auto md:h-[118px] py-4 px-4 md:pt-[31px] md:pr-[30px] md:pb-[31px] md:pl-[30px] flex items-center justify-center mx-auto">
            <h3 className="text-[16px] md:text-[20px] font-medium text-white text-center">
              AI-Generated Analysis with
              <br />
              No Bookmaker Bias
            </h3>
          </div>
        </div>
      </div>
    </section>
  )
}






